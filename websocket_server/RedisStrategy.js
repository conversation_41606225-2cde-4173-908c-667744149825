/* eslint-disable no-console */

/**
 * SPDX-FileCopyrightText: 2024 Nextcloud GmbH and Nextcloud contributors
 * SPDX-License-Identifier: AGPL-3.0-or-later
 */

import StorageStrategy from './StorageStrategy.js'
import { createClient } from 'redis'
import Config from './Config.js'

export default class RedisStrategy extends StorageStrategy {

	static createRedisClient() {
		console.log(`Creating Redis client with URL: ${Config.REDIS_URL}`)
		return createClient({
			url: Config.REDIS_URL,
			socket: {
				connectTimeout: 5000, // 5 second connection timeout
				commandTimeout: 5000, // 5 second command timeout
				reconnectDelay: 1000, // 1 second reconnect delay
			},
			// Add error handling for connection issues
			retry_strategy: (options) => {
				if (options.error && options.error.code === 'ECONNREFUSED') {
					console.error('Redis connection refused')
					return new Error('Redis connection refused')
				}
				if (options.total_retry_time > 1000 * 60 * 60) {
					console.error('Redis retry time exhausted')
					return new Error('Retry time exhausted')
				}
				if (options.attempt > 3) {
					console.error('Redis max attempts reached')
					return new Error('Max attempts reached')
				}
				// Retry after 1 second
				return Math.min(options.attempt * 100, 3000)
			}
		})
	}

	constructor(redisClient, options = {}) {
		const { prefix = 'general_', ttl = null } = options
		super()
		this.prefix = prefix
		this.ttl = ttl
		this.client = redisClient
	}

	async get(key) {
		try {
			// Add timeout to prevent hanging
			const data = await Promise.race([
				this.client.get(`${this.prefix}${key}`),
				new Promise((_, reject) =>
					setTimeout(() => reject(new Error('Redis get timeout')), 3000)
				)
			])
			if (!data) return null
			return JSON.parse(data)
		} catch (error) {
			console.error(`Error getting data for key ${key}:`, error)
			return null
		}
	}

	async set(key, value) {
		try {
			const serializedData = JSON.stringify(value)
			// Add timeout to prevent hanging
			const operation = this.ttl
				? this.client.set(`${this.prefix}${key}`, serializedData, { EX: this.ttl })
				: this.client.set(`${this.prefix}${key}`, serializedData)

			await Promise.race([
				operation,
				new Promise((_, reject) =>
					setTimeout(() => reject(new Error('Redis set timeout')), 3000)
				)
			])
		} catch (error) {
			console.error(`Error setting data for key ${key}:`, error)
		}
	}

	async delete(key) {
		try {
			// Add timeout to prevent hanging
			await Promise.race([
				this.client.del(`${this.prefix}${key}`),
				new Promise((_, reject) =>
					setTimeout(() => reject(new Error('Redis delete timeout')), 3000)
				)
			])
		} catch (error) {
			console.error(`Error deleting key ${key}:`, error)
		}
	}

	async clear() {
		try {
			// Add timeout to prevent hanging
			const keys = await Promise.race([
				this.client.keys(`${this.prefix}*`),
				new Promise((_, reject) =>
					setTimeout(() => reject(new Error('Redis keys timeout')), 3000)
				)
			])
			if (keys.length > 0) {
				await Promise.race([
					this.client.del(keys),
					new Promise((_, reject) =>
						setTimeout(() => reject(new Error('Redis del timeout')), 3000)
					)
				])
			}
		} catch (error) {
			console.error('Error clearing general data:', error)
		}
	}

}
