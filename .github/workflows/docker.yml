# SPDX-FileCopyrightText: 2021-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Docker image

on:
  release:
    types: [published]
  push:
    branches:
        - main
        - master
        - stable*

env:
  GHCR_REPO: ghcr.io/${{ github.repository_owner }}/whiteboard

jobs:
  build:
    runs-on: ubuntu-22.04
    timeout-minutes: 30

    strategy:
      matrix:
        platform:
          - linux/amd64
          - linux/arm64

    steps:
      - name: Set vars
        id: vars
        run: |
          if [[ "${{ github.event_name }}" == "release" ]]; then
            if [[ "${{ github.event.release.prerelease }}" == "true" ]]; then
              echo "version_channel=beta" >> $GITHUB_OUTPUT
            else
              echo "version_channel=stable" >> $GITHUB_OUTPUT
            fi
          else
            echo "version_channel=daily" >> $GITHUB_OUTPUT
          fi
          echo "version=${{ github.sha }}" >> $GITHUB_OUTPUT
          echo "platform=$(echo -n ${{ matrix.platform }} | sed 's/\//-/g')" >> $GITHUB_OUTPUT

      - uses: actions/checkout@v3
      - uses: docker/setup-qemu-action@v3
      - uses: docker/setup-buildx-action@v3

      - name: Login to Github Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build docker images
        uses: docker/build-push-action@v3
        with:
          context: .
          push: true
          platforms: ${{ matrix.platform }}
          provenance: false
          tags: |
            ${{ env.GHCR_REPO}}:${{ steps.vars.outputs.version }}-${{ steps.vars.outputs.platform }}

  release:
    runs-on: ubuntu-22.04
    timeout-minutes: 10
    needs: build

    steps:
      - name: Set vars
        id: vars
        run: |
          if [[ "${{ github.event_name }}" == "release" ]]; then
            if [[ "${{ github.event.release.prerelease }}" == "true" ]]; then
              echo "version_channel=beta" >> $GITHUB_OUTPUT
            else
              echo "version_channel=stable" >> $GITHUB_OUTPUT
            fi
          else
            echo "version_channel=daily" >> $GITHUB_OUTPUT
          fi
          echo "version_ref=${{ github.event_name == 'release' && github.ref_name || github.sha }}" >> $GITHUB_OUTPUT
          echo "version=${{ github.sha }}" >> $GITHUB_OUTPUT

      - name: Login to Github Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Create GHCR manifest
        run: |
          # Create manifest for the specific version (tag name or commit SHA)
          docker manifest create $GHCR_REPO:${{ steps.vars.outputs.version_ref }} \
            $GHCR_REPO:${{ steps.vars.outputs.version }}-linux-amd64 \
            $GHCR_REPO:${{ steps.vars.outputs.version }}-linux-arm64

          # Create manifest for the channel (release, prerelease, or daily)
          docker manifest create $GHCR_REPO:${{ steps.vars.outputs.version_channel }} \
            $GHCR_REPO:${{ steps.vars.outputs.version }}-linux-amd64 \
            $GHCR_REPO:${{ steps.vars.outputs.version }}-linux-arm64

          # Create 'latest' tag only for stable releases (not pre-releases)
          if [[ "${{ steps.vars.outputs.version_channel }}" == "stable" ]]; then
            docker manifest create $GHCR_REPO:latest \
              $GHCR_REPO:${{ steps.vars.outputs.version }}-linux-amd64 \
              $GHCR_REPO:${{ steps.vars.outputs.version }}-linux-arm64
          fi

      - name: Push manifests
        run: |
          docker manifest push $GHCR_REPO:${{ steps.vars.outputs.version_ref }}
          docker manifest push $GHCR_REPO:${{ steps.vars.outputs.version_channel }}

          # Push 'latest' tag only for stable releases
          if [[ "${{ steps.vars.outputs.version_channel }}" == "stable" ]]; then
            docker manifest push $GHCR_REPO:latest
          fi